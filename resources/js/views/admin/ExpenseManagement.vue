<template>
  <dashboard-layout title="Expense Management">
    <template #sidebar>
      <router-link
        to="/admin/expenses"
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        Expenses
      </router-link>
      <router-link
        to="/admin/incomes"
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        Incomes
      </router-link>
      <router-link
        to="/admin/users"
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        Users
      </router-link>
    </template>

    <!-- Filters -->
    <div class="bg-white p-6 rounded-lg shadow-sm mb-6">
      <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Expense Type</label>
          <select v-model="filters.type" class="w-full px-3 py-2 border border-gray-300 rounded-md">
            <option value="">All Types</option>
            <option value="building_services">Building Services</option>
            <option value="building_electricity">Building Electricity</option>
            <option value="personal_electricity">Personal Electricity</option>
            <option value="water">Water</option>
            <option value="other">Other</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Month</label>
          <select v-model="filters.month" class="w-full px-3 py-2 border border-gray-300 rounded-md">
            <option value="">All Months</option>
            <option value="01">January</option>
            <option value="02">February</option>
            <option value="03">March</option>
            <option value="04">April</option>
            <option value="05">May</option>
            <option value="06">June</option>
            <option value="07">July</option>
            <option value="08">August</option>
            <option value="09">September</option>
            <option value="10">October</option>
            <option value="11">November</option>
            <option value="12">December</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Year</label>
          <input
            type="number"
            v-model="filters.year"
            class="w-full px-3 py-2 border border-gray-300 rounded-md"
            min="2023"
            max="2025"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
          <select v-model="filters.status" class="w-full px-3 py-2 border border-gray-300 rounded-md">
            <option value="">All Status</option>
            <option value="pending">Pending</option>
            <option value="paid">Paid</option>
            <option value="overdue">Overdue</option>
          </select>
        </div>

        <div class="flex items-end">
          <button
            @click="applyFilters"
            class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Apply Filters
          </button>
        </div>
      </div>

      <div class="mt-4 flex justify-between items-center">
        <router-link
          to="/admin/expenses/create"
          class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
        >
          Create New Expense
        </router-link>

        <button
          @click="generateMonthlyExpenses"
          :disabled="generatingExpenses"
          class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:bg-gray-400"
        >
          {{ generatingExpenses ? 'Generating...' : 'Generate Monthly Expenses (70 ILS)' }}
        </button>
      </div>
    </div>

    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
      <div class="bg-white p-6 rounded-lg shadow-sm">
        <h3 class="text-lg font-semibold text-gray-700">Total Expenses</h3>
        <p class="text-3xl font-bold text-red-600">${{ totalExpenses.toFixed(2) }}</p>
      </div>
      <div class="bg-white p-6 rounded-lg shadow-sm">
        <h3 class="text-lg font-semibold text-gray-700">Paid</h3>
        <p class="text-3xl font-bold text-green-600">${{ totalPaid.toFixed(2) }}</p>
      </div>
      <div class="bg-white p-6 rounded-lg shadow-sm">
        <h3 class="text-lg font-semibold text-gray-700">Pending</h3>
        <p class="text-3xl font-bold text-orange-600">${{ totalRemaining.toFixed(2) }}</p>
      </div>
    </div>

    <!-- Expenses Table -->
    <data-table
      title="Expense Records"
      :columns="columns"
      :items="expenses"
      :loading="loading"
    >
      <template #actions="{ item }">
        <div class="flex space-x-2">
          <button
            @click="editExpense(item)"
            class="text-indigo-600 hover:text-indigo-900"
          >
            Edit
          </button>
          <button
            @click="deleteExpense(item)"
            class="text-red-600 hover:text-red-900"
          >
            Delete
          </button>
        </div>
      </template>
    </data-table>

    <!-- Pagination -->
    <div class="mt-6 flex justify-between items-center">
      <div class="text-sm text-gray-700">
        Showing {{ pagination.from }} to {{ pagination.to }} of {{ pagination.total }} results
      </div>
      <div class="flex space-x-2">
        <button
          @click="previousPage"
          :disabled="pagination.currentPage === 1"
          class="px-3 py-1 border rounded disabled:opacity-50"
        >
          Previous
        </button>
        <button
          @click="nextPage"
          :disabled="pagination.currentPage === pagination.lastPage"
          class="px-3 py-1 border rounded disabled:opacity-50"
        >
          Next
        </button>
      </div>
    </div>

    <!-- Edit Modal -->
    <div v-if="showEditModal" class="fixed inset-0 z-50 overflow-y-auto">
      <!-- Background overlay -->
      <div class="fixed inset-0 bg-black bg-opacity-50" @click="closeEditModal"></div>

      <!-- Modal content -->
      <div class="flex items-center justify-center min-h-screen p-4">
        <div class="relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-screen overflow-y-auto">
          <!-- Close button -->
          <button
            @click="closeEditModal"
            class="absolute top-4 right-4 text-gray-400 hover:text-gray-600 z-10"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>

          <!-- Modal header -->
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">
              Edit Expense
            </h3>
          </div>

          <!-- Modal body -->
          <div class="p-6">
            <expense-form
              :expense="selectedExpense"
              :is-edit="true"
              @success="handleEditSuccess"
              @error="handleEditError"
              @cancel="closeEditModal"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Notifications -->
    <notification
      :show="showNotification"
      :type="notificationType"
      :title="notificationTitle"
      :message="notificationMessage"
      @close="closeNotification"
    />
  </dashboard-layout>
</template>

<script>
import DashboardLayout from '../../components/DashboardLayout.vue';
import DataTable from '../../components/DataTable.vue';
import ExpenseForm from '../../components/ExpenseForm.vue';
import Notification from '../../components/Notification.vue';

export default {
  components: {
    DashboardLayout,
    DataTable,
    ExpenseForm,
    Notification
  },
  data() {
    return {
      loading: false,
      generatingExpenses: false,
      expenses: [],
      selectedExpense: null,
      showEditModal: false,
      showNotification: false,
      notificationType: 'info',
      notificationTitle: '',
      notificationMessage: '',
      filters: {
        type: '',
        month: '',
        year: new Date().getFullYear(),
        status: ''
      },
      pagination: {
        currentPage: 1,
        lastPage: 1,
        from: 0,
        to: 0,
        total: 0
      },
      columns: [
        { key: 'expense_type.name', label: 'Type' },
        { key: 'user.name', label: 'Neighbor' },
        { key: 'user.apartment_number', label: 'Apartment' },
        { key: 'month', label: 'Month' },
        { key: 'year', label: 'Year' },
        { key: 'amount', label: 'Amount' },
        { key: 'is_automatic', label: 'Auto' },
        { key: 'notes', label: 'Notes' }
      ]
    };
  },
  computed: {
    totalExpenses() {
      return this.expenses.reduce((total, expense) => total + parseFloat(expense.amount), 0);
    },
    totalPaid() {
      return this.expenses
        .filter(expense => expense.status === 'paid')
        .reduce((total, expense) => total + parseFloat(expense.amount), 0);
    },
    totalRemaining() {
      return this.expenses
        .filter(expense => expense.status === 'pending')
        .reduce((total, expense) => total + parseFloat(expense.amount), 0);
    }
  },
  created() {
    this.loadExpenses();
  },
  methods: {
    async loadExpenses() {
      this.loading = true;
      try {
        const response = await this.$axios.get('/expenses', {
          params: {
            page: this.pagination.currentPage,
            ...this.filters
          }
        });

        this.expenses = response.data.data;
        this.pagination = {
          currentPage: response.data.current_page,
          lastPage: response.data.last_page,
          from: response.data.from,
          to: response.data.to,
          total: response.data.total
        };
      } catch (error) {
        this.showError('Error loading expenses');
      } finally {
        this.loading = false;
      }
    },
    applyFilters() {
      this.pagination.currentPage = 1;
      this.loadExpenses();
    },
    previousPage() {
      if (this.pagination.currentPage > 1) {
        this.pagination.currentPage--;
        this.loadExpenses();
      }
    },
    nextPage() {
      if (this.pagination.currentPage < this.pagination.lastPage) {
        this.pagination.currentPage++;
        this.loadExpenses();
      }
    },
    editExpense(expense) {
      this.selectedExpense = expense;
      this.showEditModal = true;
    },
    async deleteExpense(expense) {
      if (confirm('Are you sure you want to delete this expense?')) {
        try {
          await this.$axios.delete(`/expenses/${expense.id}`);
          this.showSuccess('Deleted', 'Expense deleted successfully');
          this.loadExpenses();
        } catch (error) {
          this.showError('Delete Failed', 'Failed to delete expense');
        }
      }
    },
    handleEditSuccess() {
      this.showSuccess('Updated', 'Expense updated successfully');
      this.closeEditModal();
      this.loadExpenses();
    },
    handleEditError(message) {
      this.showError('Update Failed', message);
    },
    closeEditModal() {
      this.showEditModal = false;
      this.selectedExpense = null;
    },
    showSuccess(title, message) {
      this.notificationType = 'success';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    showError(title, message) {
      this.notificationType = 'error';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    closeNotification() {
      this.showNotification = false;
    },
    async generateMonthlyExpenses() {
      const currentMonth = new Date().getMonth() + 1;
      const currentYear = new Date().getFullYear();
      const formattedMonth = currentMonth.toString().padStart(2, '0');

      if (confirm(`Generate monthly expenses (70 ILS) for all neighbors for ${formattedMonth}/${currentYear}?`)) {
        this.generatingExpenses = true;
        try {
          await this.$axios.post('/expenses/generate-monthly', {
            month: formattedMonth,
            year: currentYear.toString()
          });

          this.showSuccess('Generated', 'Monthly expenses generated successfully');
          this.loadExpenses();
        } catch (error) {
          this.showError('Generation Failed', error.response?.data?.message || 'Failed to generate monthly expenses');
        } finally {
          this.generatingExpenses = false;
        }
      }
    }
  }
};
</script> 