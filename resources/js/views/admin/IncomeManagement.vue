<template>
  <dashboard-layout title="Income Management">
    <template #sidebar>
      <router-link 
        to="/admin/expenses" 
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        Expenses
      </router-link>
      <router-link 
        to="/admin/incomes" 
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        Incomes
      </router-link>
      <router-link 
        to="/admin/users" 
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        Users
      </router-link>
    </template>

    <!-- Filters -->
    <div class="bg-white p-6 rounded-lg shadow-sm mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">From Date</label>
          <input
            type="date"
            v-model="filters.date_from"
            class="w-full px-3 py-2 border border-gray-300 rounded-md"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">To Date</label>
          <input
            type="date"
            v-model="filters.date_to"
            class="w-full px-3 py-2 border border-gray-300 rounded-md"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
          <select v-model="filters.status" class="w-full px-3 py-2 border border-gray-300 rounded-md">
            <option value="">All Status</option>
            <option value="received">Received</option>
            <option value="pending">Pending</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>

        <div class="flex items-end">
          <button
            @click="applyFilters"
            class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Apply Filters
          </button>
        </div>
      </div>

      <div class="mt-4 flex justify-between items-center">
        <router-link 
          to="/admin/incomes/create"
          class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
        >
          Record New Income
        </router-link>
      </div>
    </div>

    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
      <div class="bg-white p-6 rounded-lg shadow-sm">
        <h3 class="text-lg font-semibold text-gray-700">Total Income</h3>
        <p class="text-3xl font-bold text-green-600">₪{{ totalIncome.toFixed(2) }}</p>
      </div>
      <div class="bg-white p-6 rounded-lg shadow-sm">
        <h3 class="text-lg font-semibold text-gray-700">This Month</h3>
        <p class="text-3xl font-bold text-blue-600">₪{{ thisMonthIncome.toFixed(2) }}</p>
      </div>
      <div class="bg-white p-6 rounded-lg shadow-sm">
        <h3 class="text-lg font-semibold text-gray-700">Last 30 Days</h3>
        <p class="text-3xl font-bold text-purple-600">₪{{ last30DaysIncome.toFixed(2) }}</p>
      </div>
    </div>

    <!-- Income Table -->
    <data-table
      title="Income Records"
      :columns="columns"
      :items="incomes"
      :loading="loading"
    >
      <template #actions="{ item }">
        <div class="flex space-x-2">
          <button 
            @click="editIncome(item)"
            class="text-indigo-600 hover:text-indigo-900"
          >
            Edit
          </button>
          <button 
            @click="deleteIncome(item)"
            class="text-red-600 hover:text-red-900"
          >
            Delete
          </button>
        </div>
      </template>
    </data-table>

    <!-- Pagination -->
    <div class="mt-6 flex justify-between items-center">
      <div class="text-sm text-gray-700">
        Showing {{ pagination.from }} to {{ pagination.to }} of {{ pagination.total }} results
      </div>
      <div class="flex space-x-2">
        <button 
          @click="previousPage"
          :disabled="pagination.currentPage === 1"
          class="px-3 py-1 border rounded disabled:opacity-50"
        >
          Previous
        </button>
        <button 
          @click="nextPage"
          :disabled="pagination.currentPage === pagination.lastPage"
          class="px-3 py-1 border rounded disabled:opacity-50"
        >
          Next
        </button>
      </div>
    </div>

    <!-- Edit Modal -->
    <div v-if="showEditModal" class="fixed inset-0 z-50 overflow-y-auto">
      <!-- Background overlay -->
      <div class="fixed inset-0 bg-black bg-opacity-50" @click="closeEditModal"></div>
      
      <!-- Modal content -->
      <div class="flex items-center justify-center min-h-screen p-4">
        <div class="relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-screen overflow-y-auto">
          <!-- Close button -->
          <button 
            @click="closeEditModal"
            class="absolute top-4 right-4 text-gray-400 hover:text-gray-600 z-10"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
          
          <!-- Modal header -->
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">
              Edit Income
            </h3>
          </div>
          
          <!-- Modal body -->
          <div class="p-6">
            <income-form
              :income="selectedIncome"
              :is-edit="true"
              @success="handleEditSuccess"
              @error="handleEditError"
              @cancel="closeEditModal"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Notifications -->
    <notification
      :show="showNotification"
      :type="notificationType"
      :title="notificationTitle"
      :message="notificationMessage"
      @close="closeNotification"
    />
  </dashboard-layout>
</template>

<script>
import DashboardLayout from '../../components/DashboardLayout.vue';
import DataTable from '../../components/DataTable.vue';
import IncomeForm from '../../components/IncomeForm.vue';
import Notification from '../../components/Notification.vue';

export default {
  components: {
    DashboardLayout,
    DataTable,
    IncomeForm,
    Notification
  },
  data() {
    return {
      loading: false,
      incomes: [],
      selectedIncome: null,
      showEditModal: false,
      showNotification: false,
      notificationType: 'info',
      notificationTitle: '',
      notificationMessage: '',
      filters: {
        date_from: '',
        date_to: '',
        status: ''
      },
      pagination: {
        currentPage: 1,
        lastPage: 1,
        from: 0,
        to: 0,
        total: 0
      },
      columns: [
        { key: 'user.name', label: 'Neighbor' },
        { key: 'user.apartment_number', label: 'Apartment' },
        { key: 'amount', label: 'Amount' },
        { key: 'payment_date', label: 'Payment Date' },
        { key: 'payment_method', label: 'Method' },
        { key: 'status', label: 'Status' },
        { key: 'notes', label: 'Notes' }
      ]
    };
  },
  computed: {
    totalIncome() {
      return this.incomes.reduce((total, income) => total + parseFloat(income.amount), 0);
    },
    thisMonthIncome() {
      const currentMonth = new Date().getMonth() + 1;
      const currentYear = new Date().getFullYear();
      return this.incomes
        .filter(income => {
          const paymentDate = new Date(income.payment_date);
          return paymentDate.getMonth() + 1 === currentMonth &&
                 paymentDate.getFullYear() === currentYear;
        })
        .reduce((total, income) => total + parseFloat(income.amount), 0);
    },
    last30DaysIncome() {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      return this.incomes
        .filter(income => {
          const paymentDate = new Date(income.payment_date);
          return paymentDate >= thirtyDaysAgo;
        })
        .reduce((total, income) => total + parseFloat(income.amount), 0);
    }
  },
  created() {
    this.loadIncomes();
  },
  methods: {
    async loadIncomes() {
      this.loading = true;
      try {
        const response = await this.$axios.get('/incomes', {
          params: {
            page: this.pagination.currentPage,
            ...this.filters
          }
        });
        
        this.incomes = response.data.data;
        this.pagination = {
          currentPage: response.data.current_page,
          lastPage: response.data.last_page,
          from: response.data.from,
          to: response.data.to,
          total: response.data.total
        };
      } catch (error) {
        this.showError('Error loading incomes');
      } finally {
        this.loading = false;
      }
    },
    applyFilters() {
      this.pagination.currentPage = 1;
      this.loadIncomes();
    },
    previousPage() {
      if (this.pagination.currentPage > 1) {
        this.pagination.currentPage--;
        this.loadIncomes();
      }
    },
    nextPage() {
      if (this.pagination.currentPage < this.pagination.lastPage) {
        this.pagination.currentPage++;
        this.loadIncomes();
      }
    },
    editIncome(income) {
      this.selectedIncome = income;
      this.showEditModal = true;
    },
    async deleteIncome(income) {
      if (confirm('Are you sure you want to delete this income record?')) {
        try {
          await this.$axios.delete(`/incomes/${income.id}`);
          this.showSuccess('Deleted', 'Income record deleted successfully');
          this.loadIncomes();
        } catch (error) {
          this.showError('Delete Failed', 'Failed to delete income record');
        }
      }
    },
    handleEditSuccess() {
      this.showSuccess('Updated', 'Income record updated successfully');
      this.closeEditModal();
      this.loadIncomes();
    },
    handleEditError(message) {
      this.showError('Update Failed', message);
    },
    closeEditModal() {
      this.showEditModal = false;
      this.selectedIncome = null;
    },
    showSuccess(title, message) {
      this.notificationType = 'success';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    showError(title, message) {
      this.notificationType = 'error';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    closeNotification() {
      this.showNotification = false;
    }
  }
};
</script>
