<template>
  <dashboard-layout title="User Management">
    <template #sidebar>
      <router-link 
        to="/admin/expenses" 
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        Expenses
      </router-link>
      <router-link 
        to="/admin/incomes" 
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        Incomes
      </router-link>
      <router-link 
        to="/admin/users" 
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        Users
      </router-link>
    </template>

    <!-- Filters -->
    <div class="bg-white p-6 rounded-lg shadow-sm mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Role</label>
          <select v-model="filters.role" class="w-full px-3 py-2 border border-gray-300 rounded-md">
            <option value="">All Roles</option>
            <option value="admin">Admin</option>
            <option value="neighbor">Neighbor</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Search</label>
          <input
            type="text"
            v-model="filters.search"
            placeholder="Search by name or email..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Apartment</label>
          <input
            type="text"
            v-model="filters.apartment"
            placeholder="Apartment number..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md"
          />
        </div>

        <div class="flex items-end">
          <button
            @click="applyFilters"
            class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Apply Filters
          </button>
        </div>
      </div>

      <div class="mt-4 flex justify-between items-center">
        <button 
          @click="showCreateModal = true"
          class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
        >
          Create New User
        </button>
      </div>
    </div>

    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
      <div class="bg-white p-6 rounded-lg shadow-sm">
        <h3 class="text-lg font-semibold text-gray-700">Total Users</h3>
        <p class="text-3xl font-bold text-blue-600">{{ totalUsers }}</p>
      </div>
      <div class="bg-white p-6 rounded-lg shadow-sm">
        <h3 class="text-lg font-semibold text-gray-700">Admins</h3>
        <p class="text-3xl font-bold text-purple-600">{{ totalAdmins }}</p>
      </div>
      <div class="bg-white p-6 rounded-lg shadow-sm">
        <h3 class="text-lg font-semibold text-gray-700">Neighbors</h3>
        <p class="text-3xl font-bold text-green-600">{{ totalNeighbors }}</p>
      </div>
    </div>

    <!-- Users Table -->
    <data-table
      title="User Records"
      :columns="columns"
      :items="users"
      :loading="loading"
    >
      <template #actions="{ item }">
        <div class="flex space-x-2">
          <button 
            @click="editUser(item)"
            class="text-indigo-600 hover:text-indigo-900"
          >
            Edit
          </button>
          <button 
            @click="deleteUser(item)"
            class="text-red-600 hover:text-red-900"
          >
            Delete
          </button>
        </div>
      </template>
    </data-table>

    <!-- Pagination -->
    <div class="mt-6 flex justify-between items-center">
      <div class="text-sm text-gray-700">
        Showing {{ pagination.from }} to {{ pagination.to }} of {{ pagination.total }} results
      </div>
      <div class="flex space-x-2">
        <button 
          @click="previousPage"
          :disabled="pagination.currentPage === 1"
          class="px-3 py-1 border rounded disabled:opacity-50"
        >
          Previous
        </button>
        <button 
          @click="nextPage"
          :disabled="pagination.currentPage === pagination.lastPage"
          class="px-3 py-1 border rounded disabled:opacity-50"
        >
          Next
        </button>
      </div>
    </div>

    <!-- Create/Edit Modal -->
    <div v-if="showCreateModal || showEditModal" class="fixed inset-0 z-50 overflow-y-auto">
      <!-- Background overlay -->
      <div class="fixed inset-0 bg-black bg-opacity-50" @click="closeModal"></div>
      
      <!-- Modal content -->
      <div class="flex items-center justify-center min-h-screen p-4">
        <div class="relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-screen overflow-y-auto">
          <!-- Close button -->
          <button 
            @click="closeModal"
            class="absolute top-4 right-4 text-gray-400 hover:text-gray-600 z-10"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
          
          <!-- Modal header -->
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">
              {{ showEditModal ? 'Edit User' : 'Create User' }}
            </h3>
          </div>
          
          <!-- Modal body -->
          <div class="p-6">
            <user-form
              :user="selectedUser"
              :is-edit="showEditModal"
              @success="handleFormSuccess"
              @error="handleFormError"
              @cancel="closeModal"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Notifications -->
    <notification
      :show="showNotification"
      :type="notificationType"
      :title="notificationTitle"
      :message="notificationMessage"
      @close="closeNotification"
    />
  </dashboard-layout>
</template>

<script>
import DashboardLayout from '../../components/DashboardLayout.vue';
import DataTable from '../../components/DataTable.vue';
import UserForm from '../../components/UserForm.vue';
import Notification from '../../components/Notification.vue';

export default {
  components: {
    DashboardLayout,
    DataTable,
    UserForm,
    Notification
  },
  data() {
    return {
      loading: false,
      users: [],
      selectedUser: null,
      showCreateModal: false,
      showEditModal: false,
      showNotification: false,
      notificationType: 'info',
      notificationTitle: '',
      notificationMessage: '',
      filters: {
        role: '',
        search: '',
        apartment: ''
      },
      pagination: {
        currentPage: 1,
        lastPage: 1,
        from: 0,
        to: 0,
        total: 0
      },
      columns: [
        { key: 'name', label: 'Name' },
        { key: 'email', label: 'Email' },
        { key: 'apartment_number', label: 'Apartment' },
        { key: 'role', label: 'Role' },
        { key: 'created_at', label: 'Created At' }
      ]
    };
  },
  computed: {
    totalUsers() {
      return this.users.length;
    },
    totalAdmins() {
      return this.users.filter(user => user.role === 'admin').length;
    },
    totalNeighbors() {
      return this.users.filter(user => user.role === 'neighbor').length;
    }
  },
  created() {
    this.loadUsers();
  },
  methods: {
    async loadUsers() {
      this.loading = true;
      try {
        const response = await this.$axios.get('/admin/users', {
          params: {
            page: this.pagination.currentPage,
            ...this.filters
          }
        });
        
        this.users = response.data.data;
        this.pagination = {
          currentPage: response.data.current_page,
          lastPage: response.data.last_page,
          from: response.data.from,
          to: response.data.to,
          total: response.data.total
        };
      } catch (error) {
        this.showError('Error loading users');
      } finally {
        this.loading = false;
      }
    },
    applyFilters() {
      this.pagination.currentPage = 1;
      this.loadUsers();
    },
    previousPage() {
      if (this.pagination.currentPage > 1) {
        this.pagination.currentPage--;
        this.loadUsers();
      }
    },
    nextPage() {
      if (this.pagination.currentPage < this.pagination.lastPage) {
        this.pagination.currentPage++;
        this.loadUsers();
      }
    },
    editUser(user) {
      this.selectedUser = user;
      this.showEditModal = true;
    },
    async deleteUser(user) {
      if (confirm('Are you sure you want to delete this user?')) {
        try {
          await this.$axios.delete(`/admin/users/${user.id}`);
          this.showSuccess('Deleted', 'User deleted successfully');
          this.loadUsers();
        } catch (error) {
          this.showError('Delete Failed', 'Failed to delete user');
        }
      }
    },
    handleFormSuccess() {
      this.showSuccess(this.showEditModal ? 'Updated' : 'Created', 
                      `User ${this.showEditModal ? 'updated' : 'created'} successfully`);
      this.closeModal();
      this.loadUsers();
    },
    handleFormError(message) {
      this.showError(this.showEditModal ? 'Update Failed' : 'Create Failed', message);
    },
    closeModal() {
      this.showCreateModal = false;
      this.showEditModal = false;
      this.selectedUser = null;
    },
    showSuccess(title, message) {
      this.notificationType = 'success';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    showError(title, message) {
      this.notificationType = 'error';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    closeNotification() {
      this.showNotification = false;
    }
  }
};
</script>
