<template>
  <div class="building-management">
    <h1 class="text-2xl font-bold mb-6">Building Management</h1>
    
    <div v-if="isSuperAdmin">
      <div class="mb-4 flex justify-between items-center">
        <button 
          @click="openCreateModal" 
          class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
        >
          Add New Building
        </button>
      </div>

      <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Address</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">City</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Country</th>
              <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="building in buildings" :key="building.id">
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ building.name }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ building.address || '-' }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ building.city || '-' }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ building.country || '-' }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <button @click="editBuilding(building)" class="text-indigo-600 hover:text-indigo-900 mr-2">Edit</button>
                <button @click="deleteBuilding(building.id)" class="text-red-600 hover:text-red-900">Delete</button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Modal for Create/Edit Building -->
      <div v-if="showModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
          <h2 class="text-xl font-bold mb-4">{{ editMode ? 'Edit Building' : 'Add New Building' }}</h2>
          <form @submit.prevent="saveBuilding">
            <div class="mb-4">
              <label for="name" class="block text-sm font-medium text-gray-700">Name</label>
              <input type="text" id="name" v-model="currentBuilding.name" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" required>
            </div>
            <div class="mb-4">
              <label for="address" class="block text-sm font-medium text-gray-700">Address</label>
              <input type="text" id="address" v-model="currentBuilding.address" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
            </div>
            <div class="mb-4">
              <label for="city" class="block text-sm font-medium text-gray-700">City</label>
              <input type="text" id="city" v-model="currentBuilding.city" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
            </div>
            <div class="mb-4">
              <label for="country" class="block text-sm font-medium text-gray-700">Country</label>
              <input type="text" id="country" v-model="currentBuilding.country" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
            </div>
            <div class="mb-4">
              <label for="postal_code" class="block text-sm font-medium text-gray-700">Postal Code</label>
              <input type="text" id="postal_code" v-model="currentBuilding.postal_code" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
            </div>
            <div class="mb-4">
              <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
              <textarea id="description" v-model="currentBuilding.description" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"></textarea>
            </div>
            <div class="flex justify-end">
              <button type="button" @click="closeModal" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded mr-2">Cancel</button>
              <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Save</button>
            </div>
          </form>
        </div>
      </div>
    </div>
    <div v-else class="text-red-500 text-lg">
      Access Denied: You do not have permission to manage buildings.
    </div>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'BuildingManagement',
  data() {
    return {
      buildings: [],
      showModal: false,
      editMode: false,
      currentBuilding: {
        id: null,
        name: '',
        address: '',
        city: '',
        country: '',
        postal_code: '',
        description: ''
      },
      user: null,
      isSuperAdmin: false
    };
  },
  mounted() {
    this.fetchUser();
    this.fetchBuildings();
  },
  methods: {
    fetchUser() {
      axios.get('/user')
        .then(response => {
          this.user = response.data;
          this.isSuperAdmin = this.user.role === 'super_admin';
        })
        .catch(error => {
          console.error('Error fetching user:', error);
        });
    },
    fetchBuildings() {
      axios.get('/buildings')
        .then(response => {
          this.buildings = response.data;
        })
        .catch(error => {
          console.error('Error fetching buildings:', error);
        });
    },
    openCreateModal() {
      this.editMode = false;
      this.currentBuilding = {
        id: null,
        name: '',
        address: '',
        city: '',
        country: '',
        postal_code: '',
        description: ''
      };
      this.showModal = true;
    },
    editBuilding(building) {
      this.editMode = true;
      this.currentBuilding = { ...building };
      this.showModal = true;
    },
    closeModal() {
      this.showModal = false;
    },
    saveBuilding() {
      if (this.editMode) {
        axios.put(`/buildings/${this.currentBuilding.id}`, this.currentBuilding)
          .then(response => {
            const index = this.buildings.findIndex(b => b.id === response.data.id);
            this.buildings[index] = response.data;
            this.closeModal();
          })
          .catch(error => {
            console.error('Error updating building:', error);
          });
      } else {
        axios.post('/buildings', this.currentBuilding)
          .then(response => {
            this.buildings.push(response.data);
            this.closeModal();
          })
          .catch(error => {
            console.error('Error creating building:', error);
          });
      }
    },
    deleteBuilding(id) {
      if (confirm('Are you sure you want to delete this building?')) {
        axios.delete(`/buildings/${id}`)
          .then(() => {
            this.buildings = this.buildings.filter(building => building.id !== id);
          })
          .catch(error => {
            console.error('Error deleting building:', error);
          });
      }
    }
  }
};
</script>

<style scoped>
.building-management {
  padding: 20px;
}
</style>
