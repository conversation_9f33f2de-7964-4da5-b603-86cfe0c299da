<template>
  <dashboard-layout title="Admin Dashboard">
    <template #sidebar>
      <router-link
        to="/admin/expenses"
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        Expenses
      </router-link>
      <router-link
        to="/admin/incomes"
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        Incomes
      </router-link>
      <router-link
        to="/admin/users"
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        Users
      </router-link>
    </template>

    <!-- Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
      <div class="bg-white p-6 rounded-lg shadow-sm">
        <h3 class="text-lg font-semibold text-gray-700">Total Expenses</h3>
        <p class="text-3xl font-bold text-red-600">${{ totalExpenses.toFixed(2) }}</p>
      </div>
      <div class="bg-white p-6 rounded-lg shadow-sm">
        <h3 class="text-lg font-semibold text-gray-700">Total Income</h3>
        <p class="text-3xl font-bold text-green-600">${{ totalIncome.toFixed(2) }}</p>
      </div>
      <div class="bg-white p-6 rounded-lg shadow-sm">
        <h3 class="text-lg font-semibold text-gray-700">Total Users</h3>
        <p class="text-3xl font-bold text-blue-600">{{ totalUsers }}</p>
      </div>
      <div class="bg-white p-6 rounded-lg shadow-sm">
        <h3 class="text-lg font-semibold text-gray-700">Outstanding Balance</h3>
        <p class="text-3xl font-bold text-orange-600">${{ outstandingBalance.toFixed(2) }}</p>
      </div>
    </div>

    <!-- Recent Expenses -->
    <data-table
      title="Recent Expenses"
      :columns="expenseColumns"
      :items="recentExpenses"
      :loading="loading"
    >
      <template #actions="{ item }">
        <router-link
          to="/admin/expenses"
          class="text-indigo-600 hover:text-indigo-900"
        >
          View All
        </router-link>
      </template>
    </data-table>

    <!-- Recent Incomes -->
    <div class="mt-6">
      <data-table
        title="Recent Income Records"
        :columns="incomeColumns"
        :items="recentIncomes"
        :loading="loading"
      >
        <template #actions="{ item }">
          <router-link
            to="/admin/incomes"
            class="text-indigo-600 hover:text-indigo-900"
          >
            View All
          </router-link>
        </template>
      </data-table>
    </div>
  </dashboard-layout>
</template>

<script>
import DashboardLayout from '../components/DashboardLayout.vue';
import DataTable from '../components/DataTable.vue';

export default {
  components: {
    DashboardLayout,
    DataTable
  },
  data() {
    return {
      loading: false,
      totalExpenses: 0,
      totalIncome: 0,
      totalUsers: 0,
      recentExpenses: [],
      recentIncomes: [],
      expenseColumns: [
        { key: 'expense_type.name', label: 'Type' },
        { key: 'user.name', label: 'Neighbor' },
        { key: 'amount', label: 'Amount' },
        { key: 'month', label: 'Month' },
        { key: 'year', label: 'Year' }
      ],
      incomeColumns: [
        { key: 'user.name', label: 'Neighbor' },
        { key: 'amount', label: 'Amount' },
        { key: 'payment_date', label: 'Payment Date' },
        { key: 'payment_method', label: 'Method' }
      ]
    };
  },
  computed: {
    outstandingBalance() {
      return this.totalExpenses - this.totalIncome;
    }
  },
  async created() {
    await this.loadDashboardData();
  },
  methods: {
    async loadDashboardData() {
      this.loading = true;
      try {
        // Load expenses data
        const expensesResponse = await this.$axios.get('/expenses');
        if (expensesResponse.data.data) {
          this.recentExpenses = expensesResponse.data.data.slice(0, 5);
          this.totalExpenses = expensesResponse.data.data.reduce((total, expense) =>
            total + parseFloat(expense.amount), 0);
        }

        // Load incomes data
        const incomesResponse = await this.$axios.get('/incomes');
        if (incomesResponse.data.data) {
          this.recentIncomes = incomesResponse.data.data.slice(0, 5);
          this.totalIncome = incomesResponse.data.data.reduce((total, income) =>
            total + parseFloat(income.amount), 0);
        }

        // Load users data
        const usersResponse = await this.$axios.get('/admin/users');
        if (usersResponse.data.data) {
          this.totalUsers = usersResponse.data.data.length;
        }

      } catch (error) {
        console.error('Error loading dashboard data:', error);
      } finally {
        this.loading = false;
      }
    }
  }
};
</script>