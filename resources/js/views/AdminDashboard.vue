<template>
  <dashboard-layout title="Admin Dashboard">
    <template #sidebar>
      <router-link
        to="/admin/expenses"
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        Expenses
      </router-link>
      <router-link
        to="/admin/incomes"
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        Incomes
      </router-link>
      <router-link
        to="/admin/users"
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        Users
      </router-link>
    </template>

    <!-- Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
      <div class="bg-white p-6 rounded-lg shadow-sm">
        <h3 class="text-lg font-semibold text-gray-700">Total Expenses</h3>
        <p class="text-3xl font-bold text-red-600">₪{{ totalExpenses.toFixed(2) }}</p>
      </div>
      <div class="bg-white p-6 rounded-lg shadow-sm">
        <h3 class="text-lg font-semibold text-gray-700">Total Income</h3>
        <p class="text-3xl font-bold text-green-600">₪{{ totalIncome.toFixed(2) }}</p>
      </div>
      <div class="bg-white p-6 rounded-lg shadow-sm">
        <h3 class="text-lg font-semibold text-gray-700">Total Users</h3>
        <p class="text-3xl font-bold text-blue-600">{{ totalUsers }}</p>
      </div>
      <div class="bg-white p-6 rounded-lg shadow-sm">
        <h3 class="text-lg font-semibold text-gray-700">Outstanding Balance</h3>
        <p class="text-3xl font-bold text-orange-600">₪{{ outstandingBalance.toFixed(2) }}</p>
      </div>
    </div>

    <!-- Neighbor Financial Summary -->
    <data-table
      title="Neighbor Financial Summary"
      :columns="neighborColumns"
      :items="neighborSummary"
      :loading="loading"
    />
  </dashboard-layout>
</template>

<script>
import DashboardLayout from '../components/DashboardLayout.vue';
import DataTable from '../components/DataTable.vue';

export default {
  components: {
    DashboardLayout,
    DataTable
  },
  data() {
    return {
      loading: false,
      totalExpenses: 0,
      totalIncome: 0,
      totalUsers: 0,
      allExpenses: [],
      allIncomes: [],
      neighborColumns: [
        { key: 'name', label: 'Neighbor' },
        { key: 'apartment_number', label: 'Apartment' },
        { key: 'total_expenses', label: 'Total Expenses' },
        { key: 'total_income', label: 'Total Income' },
        { key: 'outstanding_balance', label: 'Outstanding Balance' }
      ]
    };
  },
  computed: {
    outstandingBalance() {
      return this.totalExpenses - this.totalIncome;
    },
    neighborSummary() {
      // Create a map to group expenses and incomes by user
      const userMap = new Map();

      // Process expenses
      this.allExpenses.forEach(expense => {
        if (expense.user) {
          const userId = expense.user.id;
          if (!userMap.has(userId)) {
            userMap.set(userId, {
              id: userId,
              name: expense.user.name,
              apartment_number: expense.user.apartment_number,
              total_expenses: 0,
              total_income: 0
            });
          }
          userMap.get(userId).total_expenses += parseFloat(expense.amount);
        }
      });

      // Process incomes
      this.allIncomes.forEach(income => {
        if (income.user) {
          const userId = income.user.id;
          if (!userMap.has(userId)) {
            userMap.set(userId, {
              id: userId,
              name: income.user.name,
              apartment_number: income.user.apartment_number,
              total_expenses: 0,
              total_income: 0
            });
          }
          userMap.get(userId).total_income += parseFloat(income.amount);
        }
      });

      // Convert to array and calculate outstanding balance
      return Array.from(userMap.values()).map(user => ({
        ...user,
        outstanding_balance: user.total_expenses - user.total_income
      })).sort((a, b) => a.apartment_number.localeCompare(b.apartment_number));
    }
  },
  async created() {
    await this.loadDashboardData();
  },
  methods: {
    async loadDashboardData() {
      this.loading = true;
      try {
        // Load all expenses data
        const expensesResponse = await this.$axios.get('/expenses');
        if (expensesResponse.data.data) {
          this.allExpenses = expensesResponse.data.data;
          this.totalExpenses = expensesResponse.data.data.reduce((total, expense) =>
            total + parseFloat(expense.amount), 0);
        }

        // Load all incomes data
        const incomesResponse = await this.$axios.get('/incomes');
        if (incomesResponse.data.data) {
          this.allIncomes = incomesResponse.data.data;
          this.totalIncome = incomesResponse.data.data.reduce((total, income) =>
            total + parseFloat(income.amount), 0);
        }

        // Load users data
        const usersResponse = await this.$axios.get('/admin/users');
        if (usersResponse.data.data) {
          this.totalUsers = usersResponse.data.data.length;
        }

      } catch (error) {
        console.error('Error loading dashboard data:', error);
      } finally {
        this.loading = false;
      }
    }
  }
};
</script>