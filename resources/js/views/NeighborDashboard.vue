<template>
  <dashboard-layout title="Neighbor Dashboard">
    <template #sidebar>
      <div class="block px-4 py-2 text-gray-600 bg-indigo-50 text-indigo-600 rounded">
        My Financial Summary
      </div>
    </template>

    <!-- Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
      <div class="bg-white p-6 rounded-lg shadow-sm">
        <h3 class="text-lg font-semibold text-gray-700">Total Expenses</h3>
        <p class="text-3xl font-bold text-red-600">₪{{ totalExpenses.toFixed(2) }}</p>
      </div>
      <div class="bg-white p-6 rounded-lg shadow-sm">
        <h3 class="text-lg font-semibold text-gray-700">Total Payments</h3>
        <p class="text-3xl font-bold text-green-600">₪{{ totalPayments.toFixed(2) }}</p>
      </div>
      <div class="bg-white p-6 rounded-lg shadow-sm">
        <h3 class="text-lg font-semibold text-gray-700">Outstanding Balance</h3>
        <p class="text-3xl font-bold"
           :class="outstandingBalance > 0 ? 'text-red-600' : outstandingBalance < 0 ? 'text-green-600' : 'text-gray-600'">
          ₪{{ outstandingBalance.toFixed(2) }}
        </p>
      </div>
    </div>

    <!-- My Expenses -->
    <data-table
      title="My Expenses"
      :columns="expenseColumns"
      :items="myExpenses"
      :loading="loading"
    />

    <!-- My Income Records -->
    <div class="mt-6">
      <data-table
        title="My Income Records"
        :columns="incomeColumns"
        :items="myIncomes"
        :loading="loading"
      />
    </div>

    <!-- Notifications -->
    <notification
      :show="showNotification"
      :type="notificationType"
      :title="notificationTitle"
      :message="notificationMessage"
      @close="closeNotification"
    />
  </dashboard-layout>
</template>

<script>
import DashboardLayout from '../components/DashboardLayout.vue';
import DataTable from '../components/DataTable.vue';
import Notification from '../components/Notification.vue';

export default {
  components: {
    DashboardLayout,
    DataTable,
    Notification
  },
  data() {
    return {
      loading: false,
      myExpenses: [],
      myIncomes: [],
      showNotification: false,
      notificationType: 'info',
      notificationTitle: '',
      notificationMessage: '',
      expenseColumns: [
        { key: 'expense_type.name', label: 'Type' },
        { key: 'amount', label: 'Amount' },
        { key: 'month', label: 'Month' },
        { key: 'year', label: 'Year' },
        { key: 'status', label: 'Status' },
        { key: 'notes', label: 'Notes' }
      ],
      incomeColumns: [
        { key: 'amount', label: 'Amount' },
        { key: 'payment_date', label: 'Payment Date' },
        { key: 'payment_method', label: 'Method' },
        { key: 'status', label: 'Status' },
        { key: 'notes', label: 'Notes' }
      ]
    };
  },
  computed: {
    totalExpenses() {
      return this.myExpenses.reduce((total, expense) => total + parseFloat(expense.amount), 0);
    },
    totalPayments() {
      return this.myIncomes.reduce((total, income) => total + parseFloat(income.amount), 0);
    },
    outstandingBalance() {
      return this.totalExpenses - this.totalPayments;
    }
  },
  async created() {
    await this.loadDashboardData();
  },
  methods: {
    async loadDashboardData() {
      this.loading = true;
      try {
        // Get current user from localStorage
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        console.log('Current user:', user);

        if (!user.id) {
          this.showError('Authentication Error', 'User not found. Please log in again.');
          return;
        }

        // Load user's expenses - filter by current user
        console.log('Loading expenses for user ID:', user.id);
        const expensesResponse = await this.$axios.get('/expenses', {
          params: {
            user_id: user.id
          }
        });
        console.log('Expenses response:', expensesResponse.data);

        if (expensesResponse.data.data) {
          this.myExpenses = expensesResponse.data.data;
        } else if (Array.isArray(expensesResponse.data)) {
          this.myExpenses = expensesResponse.data;
        } else {
          this.myExpenses = [];
        }
        console.log('My expenses:', this.myExpenses);

        // Load user's income records - filter by current user
        console.log('Loading incomes for user ID:', user.id);
        const incomesResponse = await this.$axios.get('/incomes', {
          params: {
            user_id: user.id
          }
        });
        console.log('Incomes response:', incomesResponse.data);

        if (incomesResponse.data.data) {
          this.myIncomes = incomesResponse.data.data;
        } else if (Array.isArray(incomesResponse.data)) {
          this.myIncomes = incomesResponse.data;
        } else {
          this.myIncomes = [];
        }
        console.log('My incomes:', this.myIncomes);

        // Show success message if data loaded
        if (this.myExpenses.length === 0 && this.myIncomes.length === 0) {
          this.showSuccess('Data Loaded', 'No expenses or income records found for your account.');
        }

      } catch (error) {
        console.error('Error loading dashboard data:', error);
        this.showError('Error loading dashboard data', error.response?.data?.message || 'Failed to load your financial data');
      } finally {
        this.loading = false;
      }
    },
    showSuccess(title, message) {
      this.notificationType = 'success';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    showError(title, message) {
      this.notificationType = 'error';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    closeNotification() {
      this.showNotification = false;
    }
  }
};
</script> 