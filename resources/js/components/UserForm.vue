<template>
  <div>
    <h2 v-if="!isEdit" class="text-xl font-semibold text-gray-900 mb-6">Create New User</h2>
    
    <form @submit.prevent="handleSubmit" class="space-y-6">
      <div>
        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Name</label>
        <input
          type="text"
          id="name"
          v-model="formData.name"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          required
        />
      </div>

      <div>
        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email</label>
        <input
          type="email"
          id="email"
          v-model="formData.email"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          required
        />
      </div>

      <div>
        <label for="apartment_number" class="block text-sm font-medium text-gray-700 mb-2">Apartment Number</label>
        <input
          type="text"
          id="apartment_number"
          v-model="formData.apartment_number"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          required
        />
      </div>

      <div>
        <label for="role" class="block text-sm font-medium text-gray-700 mb-2">Role</label>
        <select
          id="role"
          v-model="formData.role"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          required
        >
          <option value="">Select Role</option>
          <option value="admin">Admin</option>
          <option value="neighbor">Neighbor</option>
        </select>
      </div>

      <div>
        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
          Password {{ isEdit ? '(leave blank to keep current)' : '' }}
        </label>
        <input
          type="password"
          id="password"
          v-model="formData.password"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          :required="!isEdit"
        />
      </div>

      <div>
        <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-2">Confirm Password</label>
        <input
          type="password"
          id="password_confirmation"
          v-model="formData.password_confirmation"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          :required="!isEdit"
        />
      </div>

      <div class="flex justify-end space-x-3 pt-4">
        <button
          type="button"
          @click="$emit('cancel')"
          class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          Cancel
        </button>
        <button
          type="submit"
          :disabled="processing"
          class="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {{ processing ? 'Saving...' : (isEdit ? 'Update User' : 'Create User') }}
        </button>
      </div>
    </form>
  </div>
</template>

<script>
export default {
  name: 'UserForm',
  props: {
    user: {
      type: Object,
      default: () => ({
        name: '',
        email: '',
        apartment_number: '',
        role: '',
        password: '',
        password_confirmation: ''
      })
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      processing: false,
      formData: {
        name: '',
        email: '',
        apartment_number: '',
        role: '',
        password: '',
        password_confirmation: ''
      }
    };
  },
  watch: {
    user: {
      handler(newUser) {
        if (newUser && this.isEdit) {
          this.formData = {
            name: newUser.name || '',
            email: newUser.email || '',
            apartment_number: newUser.apartment_number || '',
            role: newUser.role || '',
            password: '',
            password_confirmation: ''
          };
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    // Initialize form data if editing
    if (this.isEdit && this.user) {
      this.formData = {
        name: this.user.name || '',
        email: this.user.email || '',
        apartment_number: this.user.apartment_number || '',
        role: this.user.role || '',
        password: '',
        password_confirmation: ''
      };
    }
  },
  methods: {
    async handleSubmit() {
      this.processing = true;
      try {
        const submitData = {
          ...this.formData
        };

        // Remove empty password fields for edit
        if (this.isEdit && !submitData.password) {
          delete submitData.password;
          delete submitData.password_confirmation;
        }

        const response = await this.$axios[this.isEdit ? 'put' : 'post'](
          this.isEdit ? `/admin/users/${this.user.id}` : '/admin/users',
          submitData
        );

        this.$emit('success', response.data);
      } catch (error) {
        this.$emit('error', error.response?.data?.message || 'Failed to save user');
      } finally {
        this.processing = false;
      }
    }
  }
};
</script>
